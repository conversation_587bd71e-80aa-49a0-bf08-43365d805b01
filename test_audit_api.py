#!/usr/bin/env python3
"""
Test script for Audit Logs API functionality
This script demonstrates the audit logging system by making API calls
and showing how user actions are tracked.
"""

import requests
import json
import time
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:47001/api/v1"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def print_response(title, response):
    """Print formatted API response"""
    print(f"\n{'='*60}")
    print(f"{title}")
    print(f"{'='*60}")
    print(f"Status Code: {response.status_code}")
    
    try:
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")
    except:
        print(f"Response: {response.text}")

def test_user_registration():
    """Test user registration (should create audit log)"""
    print("\n🔹 Testing User Registration...")
    
    registration_data = {
        "email": f"testuser_{int(time.time())}@example.com",
        "firstname": "Test",
        "lastname": "User",
        "phone": "+1234567890",
        "business_name": "Test Business"
    }
    
    response = requests.post(
        f"{BASE_URL}/users/register-business/",
        headers=HEADERS,
        json=registration_data
    )
    
    print_response("User Registration", response)
    return response

def test_login_request():
    """Test login request (should create audit log)"""
    print("\n🔹 Testing Login Request...")
    
    # First, let's try with invalid credentials to test failed login logging
    login_data = {
        "email": "<EMAIL>",
        "password": "wrongpassword"
    }
    
    response = requests.post(
        f"{BASE_URL}/users/login-request/",
        headers=HEADERS,
        json=login_data
    )
    
    print_response("Login Request (Invalid Credentials)", response)
    return response

def test_audit_logs_api(access_token=None):
    """Test audit logs API endpoints"""
    print("\n🔹 Testing Audit Logs API...")
    
    # If we have an access token, use it
    auth_headers = HEADERS.copy()
    if access_token:
        auth_headers["Authorization"] = f"Bearer {access_token}"
    
    # Test 1: Get all audit logs (without authentication - should fail)
    print("\n📋 Testing audit logs without authentication...")
    response = requests.get(f"{BASE_URL}/audit/logs/", headers=HEADERS)
    print_response("Audit Logs (Unauthenticated)", response)
    
    # Test 2: Get audit logs with authentication (if token available)
    if access_token:
        print("\n📋 Testing audit logs with authentication...")
        response = requests.get(f"{BASE_URL}/audit/logs/", headers=auth_headers)
        print_response("Audit Logs (Authenticated)", response)
        
        # Test 3: Get audit log statistics
        print("\n📊 Testing audit log statistics...")
        response = requests.get(f"{BASE_URL}/audit/logs/stats/", headers=auth_headers)
        print_response("Audit Log Statistics", response)
        
        # Test 4: Get current user's logs
        print("\n👤 Testing current user's logs...")
        response = requests.get(f"{BASE_URL}/audit/logs/my_logs/", headers=auth_headers)
        print_response("My Audit Logs", response)
        
        # Test 5: Filter audit logs by action
        print("\n🔍 Testing filtered audit logs (LOGIN actions)...")
        response = requests.get(
            f"{BASE_URL}/audit/logs/?action=LOGIN", 
            headers=auth_headers
        )
        print_response("Filtered Audit Logs (LOGIN)", response)
        
        # Test 6: Search audit logs
        print("\n🔍 Testing search in audit logs...")
        response = requests.get(
            f"{BASE_URL}/audit/logs/?search=login", 
            headers=auth_headers
        )
        print_response("Search Audit Logs", response)

def test_api_endpoints():
    """Test various API endpoints to generate audit logs"""
    print("\n🔹 Testing API Endpoints to Generate Audit Logs...")
    
    # Test health check (should not create audit log)
    response = requests.get(f"{BASE_URL}/../healthz/")
    print_response("Health Check", response)
    
    # Test readiness check (should not create audit log)
    response = requests.get(f"{BASE_URL}/../readiness/")
    print_response("Readiness Check", response)

def main():
    """Main test function"""
    print("🚀 Starting Audit Logs API Test")
    print(f"⏰ Test started at: {datetime.now()}")
    print(f"🌐 Base URL: {BASE_URL}")
    
    # Test 1: User Registration (generates audit log)
    registration_response = test_user_registration()
    
    # Test 2: Login Request (generates audit log)
    login_response = test_login_request()
    
    # Test 3: Test other API endpoints
    test_api_endpoints()
    
    # Test 4: Test audit logs API (without authentication first)
    test_audit_logs_api()
    
    # Note: In a real scenario, you would:
    # 1. Complete user registration by verifying email
    # 2. Set up password
    # 3. Login to get access token
    # 4. Then test authenticated audit logs endpoints
    
    print("\n" + "="*60)
    print("📝 TEST SUMMARY")
    print("="*60)
    print("✅ User registration tested (should create REGISTER audit log)")
    print("✅ Failed login tested (should create failed LOGIN audit log)")
    print("✅ Audit logs API endpoints tested")
    print("✅ Authentication requirements verified")
    print("\n💡 To test authenticated endpoints:")
    print("   1. Complete user registration process")
    print("   2. Set up password via verification link")
    print("   3. Login to get access token")
    print("   4. Use token to access audit logs")
    
    print(f"\n⏰ Test completed at: {datetime.now()}")

if __name__ == "__main__":
    main()
