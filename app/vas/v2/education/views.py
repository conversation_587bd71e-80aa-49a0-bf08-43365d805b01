from common.decorators import merchant_onboarding_required
from rest_framework import generics, permissions, status
from rest_framework.response import Response

from .serializers import WaecLookupSerializer, WaecPurchaseSerializer


class WaecLookupView(generics.GenericAPIView):
    serializer_class = WaecLookupSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


class WaecPurchaseView(generics.GenericAPIView):
    serializer_class = WaecPurchaseSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)
