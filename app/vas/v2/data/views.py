from common.decorators import merchant_onboarding_required
from rest_framework import generics, permissions, status
from rest_framework.response import Response

from .serializers import DataLookupRequestSerializer, DataPurchaseRequestSerializer


class DataLookupView(generics.GenericAPIView):
    serializer_class = DataLookupRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


class DataPurchaseView(generics.GenericAPIView):
    serializer_class = DataPurchaseRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)
