from common.decorators import merchant_onboarding_required
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import generics, permissions, status
from rest_framework.response import Response

from .serializers import AirtimePurchaseRequestSerializer


@extend_schema_view(
    post=extend_schema(
        summary="Purchase Airtime",
        description="Endpoint to purchase airtime for a given phone number and network.",
        responses={
            200: {
                "description": "Airtime purchase successful",
                "examples": {
                    "application/json": {
                        "success": True,
                        "message": "Airtime purchased successfully",
                        "data": {
                            "reference": "1234567890",
                            "network": "MTN",
                            "service": "Airtime Purchase",
                            "phone": "+2348012345678",
                            "amount": 1000.00,
                        },
                    }
                },
            },
            400: {"description": "Invalid request data"},
            401: {"description": "Unauthorized access"},
        },
    )
)
class AirtimePurchaseView(generics.GenericAPIView):
    serializer_class = AirtimePurchaseRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)
