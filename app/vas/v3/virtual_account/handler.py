from business.models import Business
from common.enums import VasGateStatus
from vas.integrations.virtual_account import VirtualAccountVasClient
from vas.v3.virtual_account.exceptions import VirtualAccountError
from virtual_account.models import VirtualAccount


class VirtualAccountHandler:

    @staticmethod
    def create_virtual_account(business: Business, data: dict) -> dict:
        status_code, response = VirtualAccountVasClient().create_account(
            {
                "bvn": data["bvn"],
                "account_name": data["account_name"],
                "email": data["email"],
                "provider": data["bank"],
            }
        )

        if not response.status or response.status == VasGateStatus.Failed.value:
            raise VirtualAccountError(response.message)

        VirtualAccount.objects.create(
            business=business,
            account_name=response.data["account_name"],
            account_number=response.data["account_number"],
            account_email=response.data["account_email"],
            bank_code=response.data["bank_code"],
            bank_name=data["bank"],
            is_static=True,
            bvn=data["bvn"] or "***********",
            account_reference=response.data["account_reference"],
        )
        # Add to bank model and attach to the business
        return response.data
