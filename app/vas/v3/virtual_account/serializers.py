import logging

from business.models import Business
from common.responses import ApiResponse
from email_validator import EmailNotValidError, validate_email
from rest_framework import serializers
from vas.v3.virtual_account.enums import VirtualAccountBank
from vas.v3.virtual_account.handler import VirtualAccountHandler

logger = logging.getLogger(__name__)


class GenerateVirtualAccountSerializer(serializers.Serializer):
    account_name = serializers.CharField(max_length=255)
    email = serializers.EmailField(max_length=255)
    bvn = serializers.CharField(required=False)
    bank = serializers.ChoiceField(
        VirtualAccountBank.choices(), allow_null=True, required=False
    )

    def validate(self, attrs):
        data = super().validate(attrs)
        bvn = attrs.get("bvn")
        bank = attrs.get("bank")

        if len(bvn) != 11:
            raise serializers.ValidationError({"bvn": "Bvn must be 11 digits"})

        if not bank:
            data["bank"] = "wema"

        try:
            validated_email = validate_email(data["email"])
            data["email"] = validated_email.normalized
        except EmailNotValidError as e:
            raise serializers.ValidationError({"email": str(e)})

        return data

    def create(self, validated_data):
        business: Business = self.context["business"]

        result = VirtualAccountHandler().create_virtual_account(
            business, validated_data
        )

        return ApiResponse(
            message="Virtual account created successfully.",
            data={"account_details": result},
        ).to_dict()
