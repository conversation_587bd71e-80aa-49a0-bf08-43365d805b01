import django_filters
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from .models import AuditLog


class AuditLogFilter(django_filters.FilterSet):
    """
    Filter class for AuditLog model
    """
    
    # Date range filters
    date_from = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='gte',
        help_text='Filter logs from this date (YYYY-MM-DD HH:MM:SS)'
    )
    date_to = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='lte',
        help_text='Filter logs to this date (YYYY-MM-DD HH:MM:SS)'
    )
    
    # Date shortcuts
    last_24h = django_filters.BooleanFilter(
        method='filter_last_24h',
        help_text='Filter logs from last 24 hours'
    )
    last_7d = django_filters.BooleanFilter(
        method='filter_last_7d',
        help_text='Filter logs from last 7 days'
    )
    last_30d = django_filters.BooleanFilter(
        method='filter_last_30d',
        help_text='Filter logs from last 30 days'
    )
    
    # Action filters
    action = django_filters.ChoiceFilter(
        choices=AuditLog.ACTION_CHOICES,
        help_text='Filter by action type'
    )
    actions = django_filters.MultipleChoiceFilter(
        field_name='action',
        choices=AuditLog.ACTION_CHOICES,
        help_text='Filter by multiple action types'
    )
    
    # Status filters
    status = django_filters.ChoiceFilter(
        choices=AuditLog.STATUS_CHOICES,
        help_text='Filter by status'
    )
    
    # User filters
    email = django_filters.CharFilter(
        lookup_expr='icontains',
        help_text='Filter by user email (partial match)'
    )
    user_id = django_filters.CharFilter(
        field_name='user__id',
        help_text='Filter by user ID'
    )
    
    # IP address filter
    ip_address = django_filters.CharFilter(
        lookup_expr='icontains',
        help_text='Filter by IP address (partial match)'
    )
    
    # Resource filters
    resource_type = django_filters.CharFilter(
        lookup_expr='icontains',
        help_text='Filter by resource type'
    )
    resource_id = django_filters.CharFilter(
        help_text='Filter by resource ID'
    )
    
    # Success/failure filters
    successful_only = django_filters.BooleanFilter(
        method='filter_successful_only',
        help_text='Show only successful actions'
    )
    failed_only = django_filters.BooleanFilter(
        method='filter_failed_only',
        help_text='Show only failed actions'
    )
    
    class Meta:
        model = AuditLog
        fields = [
            'action', 'status', 'email', 'ip_address', 
            'resource_type', 'resource_id'
        ]
    
    def filter_last_24h(self, queryset, name, value):
        if value:
            start_time = timezone.now() - timedelta(hours=24)
            return queryset.filter(created_at__gte=start_time)
        return queryset
    
    def filter_last_7d(self, queryset, name, value):
        if value:
            start_time = timezone.now() - timedelta(days=7)
            return queryset.filter(created_at__gte=start_time)
        return queryset
    
    def filter_last_30d(self, queryset, name, value):
        if value:
            start_time = timezone.now() - timedelta(days=30)
            return queryset.filter(created_at__gte=start_time)
        return queryset
    
    def filter_successful_only(self, queryset, name, value):
        if value:
            return queryset.filter(status=AuditLog.SUCCESS)
        return queryset
    
    def filter_failed_only(self, queryset, name, value):
        if value:
            return queryset.filter(status=AuditLog.FAILED)
        return queryset
