from django.db import models
from django.utils import timezone
from common.models import AuditableModel


class AuditLog(AuditableModel):
    """
    Model to store audit logs for user actions
    """
    
    # Action types
    LOGIN = 'LOGIN'
    LOGOUT = 'LOGOUT'
    REGISTER = 'REGISTER'
    PASSWORD_CHANGE = 'PASSWORD_CHANGE'
    PASSWORD_RESET = 'PASSWORD_RESET'
    PROFILE_UPDATE = 'PROFILE_UPDATE'
    TRANSACTION_CREATE = 'TRANSACTION_CREATE'
    TRANSACTION_UPDATE = 'TRANSACTION_UPDATE'
    WALLET_CREATE = 'WALLET_CREATE'
    WALLET_UPDATE = 'WALLET_UPDATE'
    BUSINESS_CREATE = 'BUSINESS_CREATE'
    BUSINESS_UPDATE = 'BUSINESS_UPDATE'
    PIN_CHANGE = 'PIN_CHANGE'
    PIN_VERIFY = 'PIN_VERIFY'
    OTP_REQUEST = 'OTP_REQUEST'
    OTP_VERIFY = 'OTP_VERIFY'
    TWO_FA_SETUP = 'TWO_FA_SETUP'
    TWO_FA_DISABLE = 'TWO_FA_DISABLE'
    GOOGLE_AUTH = 'GOOGLE_AUTH'
    API_ACCESS = 'API_ACCESS'
    PERMISSION_CHANGE = 'PERMISSION_CHANGE'
    ACCOUNT_DISABLE = 'ACCOUNT_DISABLE'
    ACCOUNT_ENABLE = 'ACCOUNT_ENABLE'
    DATA_EXPORT = 'DATA_EXPORT'
    DATA_IMPORT = 'DATA_IMPORT'
    SYSTEM_CONFIG = 'SYSTEM_CONFIG'
    OTHER = 'OTHER'
    
    ACTION_CHOICES = [
        (LOGIN, 'Login'),
        (LOGOUT, 'Logout'),
        (REGISTER, 'Registration'),
        (PASSWORD_CHANGE, 'Password Change'),
        (PASSWORD_RESET, 'Password Reset'),
        (PROFILE_UPDATE, 'Profile Update'),
        (TRANSACTION_CREATE, 'Transaction Created'),
        (TRANSACTION_UPDATE, 'Transaction Updated'),
        (WALLET_CREATE, 'Wallet Created'),
        (WALLET_UPDATE, 'Wallet Updated'),
        (BUSINESS_CREATE, 'Business Created'),
        (BUSINESS_UPDATE, 'Business Updated'),
        (PIN_CHANGE, 'PIN Change'),
        (PIN_VERIFY, 'PIN Verification'),
        (OTP_REQUEST, 'OTP Request'),
        (OTP_VERIFY, 'OTP Verification'),
        (TWO_FA_SETUP, '2FA Setup'),
        (TWO_FA_DISABLE, '2FA Disable'),
        (GOOGLE_AUTH, 'Google Authentication'),
        (API_ACCESS, 'API Access'),
        (PERMISSION_CHANGE, 'Permission Change'),
        (ACCOUNT_DISABLE, 'Account Disable'),
        (ACCOUNT_ENABLE, 'Account Enable'),
        (DATA_EXPORT, 'Data Export'),
        (DATA_IMPORT, 'Data Import'),
        (SYSTEM_CONFIG, 'System Configuration'),
        (OTHER, 'Other'),
    ]
    
    # Status choices
    SUCCESS = 'SUCCESS'
    FAILED = 'FAILED'
    PENDING = 'PENDING'
    
    STATUS_CHOICES = [
        (SUCCESS, 'Success'),
        (FAILED, 'Failed'),
        (PENDING, 'Pending'),
    ]
    
    user = models.ForeignKey(
        'user.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='audit_logs',
        db_index=True,
        help_text='User who performed the action'
    )
    email = models.EmailField(
        db_index=True,
        help_text='Email of the user who performed the action'
    )
    action = models.CharField(
        max_length=50,
        choices=ACTION_CHOICES,
        db_index=True,
        help_text='Type of action performed'
    )
    description = models.TextField(
        help_text='Detailed description of the action'
    )
    ip_address = models.GenericIPAddressField(
        db_index=True,
        help_text='IP address from which the action was performed'
    )
    user_agent = models.TextField(
        blank=True,
        null=True,
        help_text='User agent string from the request'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=SUCCESS,
        db_index=True,
        help_text='Status of the action'
    )
    resource_type = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_index=True,
        help_text='Type of resource affected (e.g., User, Transaction, Wallet)'
    )
    resource_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_index=True,
        help_text='ID of the resource affected'
    )
    old_values = models.JSONField(
        blank=True,
        null=True,
        help_text='Previous values before the change (for updates)'
    )
    new_values = models.JSONField(
        blank=True,
        null=True,
        help_text='New values after the change (for updates)'
    )
    metadata = models.JSONField(
        blank=True,
        null=True,
        help_text='Additional metadata about the action'
    )
    session_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_index=True,
        help_text='Session ID if available'
    )
    request_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_index=True,
        help_text='Unique request ID for tracing'
    )
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['email', '-created_at']),
            models.Index(fields=['action', '-created_at']),
            models.Index(fields=['ip_address', '-created_at']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['resource_type', 'resource_id']),
        ]
        verbose_name = 'Audit Log'
        verbose_name_plural = 'Audit Logs'
    
    def __str__(self):
        return f"{self.email} - {self.get_action_display()} - {self.created_at}"
    
    @property
    def formatted_timestamp(self):
        """Return formatted timestamp for display"""
        return self.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')
    
    @classmethod
    def log_action(cls, user=None, email=None, action=None, description=None, 
                   ip_address=None, user_agent=None, status=SUCCESS, 
                   resource_type=None, resource_id=None, old_values=None, 
                   new_values=None, metadata=None, session_id=None, request_id=None):
        """
        Convenience method to create audit log entries
        """
        if user and not email:
            email = user.email
            
        return cls.objects.create(
            user=user,
            email=email,
            action=action,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            status=status,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=old_values,
            new_values=new_values,
            metadata=metadata,
            session_id=session_id,
            request_id=request_id
        )
