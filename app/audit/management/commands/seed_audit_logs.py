"""
Django management command to seed audit log data for testing purposes.

This command creates realistic audit log entries to test the audit logging system.
"""

import random
from datetime import timed<PERSON><PERSON>

from audit.models import AuditLog
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.utils import timezone

User = get_user_model()


class Command(BaseCommand):
    help = "Seed audit log data for testing purposes"

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            type=int,
            default=100,
            help="Number of audit log entries to create (default: 100)",
        )
        parser.add_argument(
            "--days",
            type=int,
            default=30,
            help="Number of days back to spread the logs (default: 30)",
        )
        parser.add_argument(
            "--users",
            type=int,
            default=5,
            help="Number of test users to create (default: 5)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing audit logs before seeding",
        )
        parser.add_argument(
            "--scenarios",
            action="store_true",
            help="Create realistic scenarios (suspicious activity, user journeys, etc.)",
        )

    def handle(self, *args, **options):
        count = options["count"]
        days = options["days"]
        users_count = options["users"]
        clear_existing = options["clear"]
        create_scenarios = options["scenarios"]

        self.stdout.write(self.style.SUCCESS("🌱 Starting audit log seeding..."))

        # Clear existing audit logs if requested
        if clear_existing:
            self.stdout.write("🗑️  Clearing existing audit logs...")
            deleted_count = AuditLog.objects.all().delete()[0]
            self.stdout.write(
                self.style.WARNING(f"   Deleted {deleted_count} existing audit logs")
            )

        # Create test users if they don't exist
        test_users = self.create_test_users(users_count)

        # Create audit log entries
        self.create_audit_logs(count, days, test_users)

        # Create realistic scenarios if requested
        if create_scenarios:
            self.create_realistic_scenarios()

        self.stdout.write(
            self.style.SUCCESS(f"Successfully seeded {count} audit log entries!")
        )

    def create_test_users(self, count):
        """Create test users for audit log entries"""
        self.stdout.write(f"👥 Creating {count} test users...")

        test_users = []
        for i in range(count):
            email = f"testuser{i+1}@example.com"

            # Check if user already exists
            user, created = User.objects.get_or_create(
                email=email,
                defaults={
                    "firstname": f"Test{i+1}",
                    "lastname": "User",
                    "role": "Business_Owner",
                    "is_active": True,
                    "verified": True,
                },
            )

            test_users.append(user)

            if created:
                self.stdout.write(f"   ✓ Created user: {email}")
            else:
                self.stdout.write(f"   → Using existing user: {email}")

        return test_users

    def create_audit_logs(self, count, days, users):
        """Create realistic audit log entries"""
        self.stdout.write(f"📝 Creating {count} audit log entries...")

        # Sample IP addresses for variety
        ip_addresses = [
            "*************",
            "*************",
            "*********",
            "***********",
            "***********",
            "************",
            "127.0.0.1",
        ]

        # Sample user agents
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0",
        ]

        # Action scenarios with realistic distributions
        action_scenarios = [
            # Login scenarios (40% of actions)
            {
                "action": AuditLog.LOGIN,
                "status": AuditLog.SUCCESS,
                "weight": 35,
                "description": "User logged in successfully",
            },
            {
                "action": AuditLog.LOGIN,
                "status": AuditLog.FAILED,
                "weight": 5,
                "description": "Login failed: Invalid credentials",
            },
            # Registration scenarios (10% of actions)
            {
                "action": AuditLog.REGISTER,
                "status": AuditLog.SUCCESS,
                "weight": 8,
                "description": "New user registered via business_email",
            },
            {
                "action": AuditLog.REGISTER,
                "status": AuditLog.FAILED,
                "weight": 2,
                "description": "Registration failed: Email already exists",
            },
            # Profile and security actions (20% of actions)
            {
                "action": AuditLog.PROFILE_UPDATE,
                "status": AuditLog.SUCCESS,
                "weight": 10,
                "description": "User profile updated",
            },
            {
                "action": AuditLog.PASSWORD_CHANGE,
                "status": AuditLog.SUCCESS,
                "weight": 5,
                "description": "Password changed successfully",
            },
            {
                "action": AuditLog.PIN_CHANGE,
                "status": AuditLog.SUCCESS,
                "weight": 3,
                "description": "Transaction PIN updated",
            },
            {
                "action": AuditLog.TWO_FA_SETUP,
                "status": AuditLog.SUCCESS,
                "weight": 2,
                "description": "2FA setup completed",
            },
            # Transaction actions (20% of actions)
            {
                "action": AuditLog.TRANSACTION_CREATE,
                "status": AuditLog.SUCCESS,
                "weight": 15,
                "description": "New transaction created",
            },
            {
                "action": AuditLog.TRANSACTION_UPDATE,
                "status": AuditLog.SUCCESS,
                "weight": 3,
                "description": "Transaction status updated",
            },
            {
                "action": AuditLog.TRANSACTION_CREATE,
                "status": AuditLog.FAILED,
                "weight": 2,
                "description": "Transaction failed: Insufficient balance",
            },
            # Business and wallet actions (10% of actions)
            {
                "action": AuditLog.BUSINESS_UPDATE,
                "status": AuditLog.SUCCESS,
                "weight": 5,
                "description": "Business information updated",
            },
            {
                "action": AuditLog.WALLET_CREATE,
                "status": AuditLog.SUCCESS,
                "weight": 3,
                "description": "New wallet created",
            },
            {
                "action": AuditLog.WALLET_UPDATE,
                "status": AuditLog.SUCCESS,
                "weight": 2,
                "description": "Wallet settings updated",
            },
        ]

        # Create weighted list for random selection
        weighted_scenarios = []
        for scenario in action_scenarios:
            weighted_scenarios.extend([scenario] * scenario["weight"])

        # Generate audit logs
        start_date = timezone.now() - timedelta(days=days)

        for i in range(count):
            # Random timestamp within the date range
            random_seconds = random.randint(0, days * 24 * 60 * 60)
            created_at = start_date + timedelta(seconds=random_seconds)

            # Select random scenario
            scenario = random.choice(weighted_scenarios)

            # Select random user
            user = random.choice(users)

            # Create metadata based on action type
            metadata = self.generate_metadata(scenario["action"])

            # Create the audit log entry
            AuditLog.objects.create(
                user=user,
                email=user.email,
                action=scenario["action"],
                description=scenario["description"],
                ip_address=random.choice(ip_addresses),
                user_agent=random.choice(user_agents),
                status=scenario["status"],
                resource_type=self.get_resource_type(scenario["action"]),
                resource_id=f"res_{random.randint(1000, 9999)}",
                metadata=metadata,
                session_id=f"sess_{random.randint(100000, 999999)}",
                request_id=f"req_{random.randint(100000, 999999)}",
                created_at=created_at,
            )

            if (i + 1) % 20 == 0:
                self.stdout.write(f"   ✓ Created {i + 1}/{count} audit logs...")

    def generate_metadata(self, action):
        """Generate realistic metadata based on action type"""
        base_metadata = {
            "timestamp": timezone.now().isoformat(),
            "user_agent_parsed": {
                "browser": random.choice(["Chrome", "Firefox", "Safari", "Edge"]),
                "os": random.choice(["Windows", "macOS", "Linux", "iOS", "Android"]),
            },
        }

        if action == AuditLog.LOGIN:
            base_metadata.update(
                {
                    "login_method": random.choice(["email_password", "google", "2fa"]),
                    "remember_me": random.choice([True, False]),
                }
            )
        elif action == AuditLog.TRANSACTION_CREATE:
            base_metadata.update(
                {
                    "amount": f"{random.uniform(10, 1000):.2f}",
                    "currency": "NGN",
                    "transaction_type": random.choice(
                        ["airtime", "data", "transfer", "bill_payment"]
                    ),
                }
            )
        elif action == AuditLog.REGISTER:
            base_metadata.update(
                {
                    "registration_type": random.choice(["business_email", "google"]),
                    "referral_code": (
                        f"REF{random.randint(1000, 9999)}"
                        if random.random() > 0.7
                        else None
                    ),
                }
            )

        return base_metadata

    def get_resource_type(self, action):
        """Get appropriate resource type based on action"""
        resource_mapping = {
            AuditLog.LOGIN: "User",
            AuditLog.LOGOUT: "User",
            AuditLog.REGISTER: "User",
            AuditLog.PROFILE_UPDATE: "User",
            AuditLog.PASSWORD_CHANGE: "User",
            AuditLog.TRANSACTION_CREATE: "Transaction",
            AuditLog.TRANSACTION_UPDATE: "Transaction",
            AuditLog.BUSINESS_CREATE: "Business",
            AuditLog.BUSINESS_UPDATE: "Business",
            AuditLog.WALLET_CREATE: "Wallet",
            AuditLog.WALLET_UPDATE: "Wallet",
        }
        return resource_mapping.get(action, "System")

    def create_realistic_scenarios(self):
        """Create some realistic audit log scenarios"""
        self.stdout.write("🎭 Creating realistic scenarios...")

        # Scenario 1: Suspicious login attempts
        suspicious_user = User.objects.filter(email__contains="testuser1").first()
        if suspicious_user:
            for i in range(5):
                AuditLog.objects.create(
                    user=suspicious_user,
                    email=suspicious_user.email,
                    action=AuditLog.LOGIN,
                    description="Login failed: Invalid credentials",
                    ip_address="*************",  # Suspicious IP
                    status=AuditLog.FAILED,
                    created_at=timezone.now() - timedelta(minutes=i * 2),
                )
            self.stdout.write("   ✓ Created suspicious login scenario")

        # Scenario 2: Successful user journey
        journey_user = User.objects.filter(email__contains="testuser2").first()
        if journey_user:
            journey_actions = [
                (AuditLog.REGISTER, "New user registered via business_email", -60),
                (AuditLog.LOGIN, "User logged in successfully", -50),
                (AuditLog.PROFILE_UPDATE, "User profile updated", -45),
                (AuditLog.BUSINESS_UPDATE, "Business information updated", -40),
                (AuditLog.WALLET_CREATE, "New wallet created", -35),
                (AuditLog.TRANSACTION_CREATE, "New transaction created", -30),
                (AuditLog.TRANSACTION_CREATE, "New transaction created", -25),
                (AuditLog.PIN_CHANGE, "Transaction PIN updated", -20),
                (AuditLog.TWO_FA_SETUP, "2FA setup completed", -15),
            ]

            for action, description, minutes_ago in journey_actions:
                AuditLog.objects.create(
                    user=journey_user,
                    email=journey_user.email,
                    action=action,
                    description=description,
                    ip_address="*************",
                    status=AuditLog.SUCCESS,
                    created_at=timezone.now() + timedelta(minutes=minutes_ago),
                )
            self.stdout.write("   ✓ Created user journey scenario")
