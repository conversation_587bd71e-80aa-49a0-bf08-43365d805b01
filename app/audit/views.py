from django.utils import timezone
from datetime import timedelta
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from .models import AuditLog
from .serializers import AuditLogSerializer,AuditLogListSerializer
from .filters import AuditLogFilter


class AuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing audit logs
    
    Provides endpoints for:
    - Listing audit logs with filtering and search
    - Retrieving individual audit log details
    - Getting audit log statistics
    """
    
    queryset = AuditLog.objects.select_related('user').all()
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AuditLogFilter
    search_fields = ['email', 'description', 'ip_address']
    ordering_fields = ['created_at', 'action', 'status', 'email']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == 'list':
            return AuditLogListSerializer
        # elif self.action == 'stats':
        #     return AuditLogStatsSerializer
        return AuditLogSerializer
    
    def get_queryset(self):
        """
        Filter queryset based on user permissions
        """
        queryset = super().get_queryset()
        user = self.request.user
        
        # If user is staff/admin, they can see all logs
        if user.is_staff or user.role in ['admin', 'super_admin']:
            return queryset
        
        # Regular users can only see their own logs
        return queryset.filter(user=user)
    
    
    @action(detail=False, methods=['get'])
    def my_logs(self, request):
        """
        Get current user's audit logs
        """
        queryset = self.get_queryset().filter(user=request.user)
        
        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = AuditLogListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = AuditLogListSerializer(queryset, many=True)
        return Response(serializer.data)
    

    # TODO: Implement export functionality
    # @action(detail=False, methods=['get'])
    # def export(self, request):
    #     """
    #     Export audit logs (for admin users only)
    #     """
    #     user = request.user
    #     if not (user.is_staff or user.role in ['admin', 'super_admin']):
    #         return Response(
    #             {'error': 'Permission denied'}, 
    #             status=status.HTTP_403_FORBIDDEN
    #         )

    #     return Response({
    #         'message': 'Export functionality not implemented yet',
    #         'note': 'This endpoint would generate downloadable audit log reports'
    #     })
    
    def list(self, request, *args, **kwargs):
        """
        Override list to add custom response format
        """
        response = super().list(request, *args, **kwargs)
        
        # Add metadata to response
        response.data = {
            'success': True,
            'message': 'Audit logs retrieved successfully',
            'data': response.data
        }
        
        return response
    
    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve to add custom response format
        """
        response = super().retrieve(request, *args, **kwargs)
        
        response.data = {
            'success': True,
            'message': 'Audit log retrieved successfully',
            'data': response.data
        }
        
        return response
