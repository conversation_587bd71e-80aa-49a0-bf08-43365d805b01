from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import permissions, viewsets
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>, SearchFilter

from .filters import AuditLogFilter
from .models import AuditLog
from .serializers import Audit<PERSON>ogListSerializer, AuditLogSerializer


class AuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing audit logs

    Provides endpoints for:
    - Listing audit logs with filtering and search
    - Retrieving individual audit log details
    - Getting audit log statistics
    """

    queryset = AuditLog.objects.select_related("user").all()
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AuditLogFilter
    search_fields = ["email", "description", "ip_address"]
    ordering_fields = ["created_at", "action", "status", "email"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == "list":
            return AuditLogListSerializer
        return AuditLogSerializer

    def get_queryset(self):
        """
        Filter queryset based on user permissions
        """
        queryset = super().get_queryset()
        user = self.request.user

        # If user is staff/admin, they can see all logs
        if user.is_staff or user.role in ["admin", "super_admin"]:
            return queryset

        # Regular users can only see their own logs(DEPENDS ON REQUIREMENT FROM ADMIN PAGE)
        # return queryset.filter(user=user)

        return queryset.none()  # RETURN NONE FOR NOW(TODO: REMOVE THIS LINE??)

    # TODO: Implement export functionality(WHAT ARE WE EXPORTING AND IN WHAT FORMAT)
    # @action(detail=False, methods=['get'])
    # def export(self, request):
    #     """
    #     Export audit logs (for admin users only)
    #     """
    #     user = request.user
    #     if not (user.is_staff or user.role in ['admin', 'super_admin']):
    #         return Response(
    #             {'error': 'Permission denied'},
    #             status=status.HTTP_403_FORBIDDEN
    #         )

    #     return Response({
    #         'message': 'Export functionality not implemented yet',
    #         'note': 'This endpoint would generate downloadable audit log reports'
    #     })

    def list(self, request, *args, **kwargs):
        """
        Override list to add custom response format
        """
        response = super().list(request, *args, **kwargs)

        # Add metadata to response
        response.data = {
            "success": True,
            "message": "Audit logs retrieved successfully",
            "data": response.data,
        }

        return response

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve to add custom response format
        """
        response = super().retrieve(request, *args, **kwargs)

        response.data = {
            "success": True,
            "message": "Audit log retrieved successfully",
            "data": response.data,
        }

        return response


# TODO : IMPELEMENT BETTER PAGINATION (CURSOR PAGINATION)
