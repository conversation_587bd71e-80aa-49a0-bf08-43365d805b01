from decimal import Decimal
from typing import Any

from common.enums import CoreServiceResponseStatus
from ledger.models import LedgerTransaction
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.exceptions import TransactionException
from transaction.models.base import Transaction
from wallet.service import WalletService

from ..dtos import CreateBaseTransaction<PERSON>arams
from .airtime import <PERSON><PERSON><PERSON><PERSON><PERSON>saction<PERSON>and<PERSON>
from .data import <PERSON>VAST<PERSON>saction<PERSON>andler
from .education import EducationVASTransactionHandler
from .electricity import ElectricityVASTransactionHandler
from .epin import EpinVASTransactionHandler
from .interface import BaseVASTransactionHandler
from .virtual_account import VirtualAccountVASTransactionHandler

VAS_HANDLER_MAP = {
    TransactionClassEnum.AIRTIME.value: AirtimeVASTransactionHandler(),
    TransactionClassEnum.DATA.value: DataVASTransactionHandler(),
    TransactionClassEnum.ELECTRICITY.value: ElectricityVASTransactionHandler(),
    TransactionClassEnum.VIRTUAL_ACCOUNT.value: VirtualAccountVASTransactionHandler(),
    TransactionClassEnum.EPIN.value: EpinVASTransaction<PERSON>and<PERSON>(),
    TransactionClassEnum.EDUCATION.value: EducationVASTransactionHandler(),
    # Add more mappings for other VAS services
}


class TransactionHandler:
    def _get_vas_handler(
        self, txn_class: TransactionClassEnum
    ) -> BaseVASTransactionHandler:
        try:
            return VAS_HANDLER_MAP[txn_class]
        except KeyError:
            raise TransactionException(f"No VAS transaction handler for: {txn_class}")

    def create_base_transaction(
        self, params: CreateBaseTransactionParams
    ) -> Transaction:
        txn = WalletService(params.wallet).debit(
            Decimal(params.amount),
            business=params.business,
            txn_class=params.txn_class,
            type=params.type,
            narration=params.narration,
            merchant_reference=params.reference,
            validate_balance=True,
            impact_wallet=True,
        )
        return txn

    def update_base_transaction_status(
        self, txn: Transaction, status: CoreServiceResponseStatus
    ) -> Transaction:
        match status:
            case CoreServiceResponseStatus.Pending.value:
                status = TransactionStatusEnum.PENDING.value
            case CoreServiceResponseStatus.Success.value:
                status = TransactionStatusEnum.SUCCESSFUL.value
            case CoreServiceResponseStatus.Failed.value:
                status = TransactionStatusEnum.FAILED.value
        txn.status = status
        txn.save()
        return txn

    def create_vas_transaction_and_ledger_entry(
        self, txn: Transaction, txn_class: TransactionClassEnum, extra_fields: dict
    ) -> tuple[Any, LedgerTransaction]:
        vas_handler = self._get_vas_handler(txn_class)
        vas_txn = vas_handler.create_vas_transaction(txn, extra_fields)
        ledger_txn = vas_handler.create_ledger_entry(txn)
        return vas_txn, ledger_txn

    def update_vas_transaction(
        self, txn: Transaction, vas_txn: Any, update_fields: dict
    ) -> Any:
        vas_handler = self._get_vas_handler(txn.txn_class)
        return vas_handler.update_vas_transaction(vas_txn, update_fields)
