from enum import Enum


class CustomEnum(Enum):
    @classmethod
    def values(cls):
        return [c.value for c in cls]

    @classmethod
    def choices(cls):
        return [(c.value, c.value) for c in cls]


class CoreServiceResponseStatus(CustomEnum):
    Pending = "pending"
    Success = "success"
    Failed = "failed"


class AirtimeNetworkEnum(CustomEnum):
    MTN = "MTN"
    GLO = "GLO"
    AIRTEL = "AIRTEL"
    ETISALAT = "9MOBILE"


class DataNetworkEnum(CustomEnum):
    MTN = "MTN"
    GLO = "GLO"
    AIRTEL = "AIRTEL"
    ETISALAT = "9MOBILE"


class ElectricityDiscoTypeEnum(CustomEnum):
    PREPAID = "PREPAID"
    POSTPAID = "POSTPAID"


class ElectricityDiscoEnum(CustomEnum):
    AbujaElectric = "AbujaElectric"
    BeninElectric = "BeninElectric"
    EnuguElectric = "EnuguElectric"
    EkoElectric = "EkoElectric"
    IbadanElectric = "IbadanElectric"
    IkejaElectric = "IkejaElectric"
    JosElectric = "JosElectric"
    PortharcourtElectric = "PortharcourtElectric"
    KadunaElectric = "KadunaElectric"
    KanoElectric = "KanoElectric"
    YolaElectric = "YolaElectric"


class ProviderEnum(CustomEnum):
    # Data, Airtime, Epin
    Mtn = "Mtn"
    Glo = "Glo"
    Airtel = "Airtel"
    Etisalat = "9Mobile"

    # Cable-tv
    Dstv = "Dstv"
    Gotv = "Gotv"
    Startimes = "Startimes"

    # Electricity
    AbujaElectric = "AbujaElectric"
    BeninElectric = "BeninElectric"
    EnuguElectric = "EnuguElectric"
    EkoElectric = "EkoElectric"
    IbadanElectric = "IbadanElectric"
    IkejaElectric = "IkejaElectric"
    JosElectric = "JosElectric"
    PortharcourtElectric = "PortharcourtElectric"
    KadunaElectric = "KadunaElectric"
    KanoElectric = "KanoElectric"
    YolaElectric = "YolaElectric"

    # Betting
    Bet9ja = "Bet9ja"
    BangBet = "BangBet"
    NairaBet = "SupaBet"
    CloudBet = "CloudBet"
    BetLion = "BetLion"
    OneXBet = "1xBet"
    MerryBet = "MerryBet"
    BetWay = "BetWay"
    BetLand = "BetLand"
    BetKing = "BetKing"
    LiveScoreBet = "LiveScoreBet"
    NaijaBet = "NaijaBet"

    # Kyc
    Nin = "Nin"
    Bvn = "Bvn"
    PhoneNumberLookup = "PhoneNumberLookup"

    # Education
    Waec = "Waec"
    Jamb = "Jamb"

    # Transfer
    Transfer = "Transfer"


class ElectricityBiller(str, CustomEnum):
    AbujaPostpaid = "abuja_electric_postpaid"
    AbujaPrepaid = "abuja_electric_prepaid"
    BeninPostpaid = "benin_electric_postpaid"
    BeninPrepaid = "benin_electric_prepaid"
    EnuguPrepaid = "enugu_electric_prepaid"
    EnuguPostpaid = "enugu_electric_postpaid"
    EkoPrepaid = "eko_electric_prepaid"
    EkoPostpaid = "eko_electric_postpaid"
    IbadanPrepaid = "ibadan_electric_prepaid"
    IbadanPostPaid = "ibadan_electric_postpaid"
    IkejaPostpaid = "ikeja_electric_postpaid"
    IkejaPrepaid = "ikeja_electric_prepaid"
    JosPrepaid = "jos_electric_prepaid"
    JosPostpaid = "jos_electric_postpaid"
    PortharcourtPrepaid = "portharcourt_electric_prepaid"
    PortharcourtPostpaid = "portharcourt_electric_postpaid"
    KadunaPrepaid = "kaduna_electric_prepaid"
    KadunaPostpaid = "kaduna_electric_postpaid"
    KanoPrepaid = "kedco_electric_prepaid"
    KanoPostpaid = "kedco_electric_postpaid"
    YolaPrepaid = "yola_electric_prepaid"
    YolaPostpaid = "yola_electric_postpaid"

    def title(self) -> str:
        return {
            self.AbujaPostpaid: "Abuja Electric (Postpaid)",
            self.AbujaPrepaid: "Abuja Electric (Prepaid)",
            self.BeninPostpaid: "Benin Electric (Postpaid)",
            self.BeninPrepaid: "Benin Electric (Prepaid)",
            self.EnuguPrepaid: "Enugu Electric (Prepaid)",
            self.EnuguPostpaid: "Enugu Electric (Postpaid)",
            self.EkoPrepaid: "Eko Electric (Prepaid)",
            self.EkoPostpaid: "Eko Electric (Postpaid)",
            self.IbadanPrepaid: "Ibadan Electric (Prepaid)",
            self.IbadanPostPaid: "Ibadan Electric (Postpaid)",
            self.IkejaPostpaid: "Ikeja Electric (Postpaid)",
            self.IkejaPrepaid: "Ikeja Electric (Prepaid)",
            self.JosPrepaid: "Jos Electric (Prepaid)",
            self.JosPostpaid: "Jos Electric (Postpaid)",
            self.KadunaPostpaid: "Kaduna Electric (Postpaid)",
            self.KadunaPrepaid: "Kaduna Electric (Prepaid)",
            self.KanoPrepaid: "Kano Electric (Prepaid)",
            self.KanoPostpaid: "Kano Electric (Postpaid)",
            self.PortharcourtPrepaid: "Port Electric Harcourt (Prepaid)",
            self.PortharcourtPostpaid: "Port Electric Harcourt (Postpaid)",
            self.YolaPrepaid: "Yola Electric (Prepaid)",
            self.YolaPostpaid: "Yola Electric (Postpaid)",
        }[self]

    def provider(self) -> ProviderEnum:
        mapping = {
            ElectricityBiller.AbujaPrepaid: ProviderEnum.AbujaElectric,
            ElectricityBiller.AbujaPostpaid: ProviderEnum.AbujaElectric,
            ElectricityBiller.BeninPrepaid: ProviderEnum.BeninElectric,
            ElectricityBiller.BeninPostpaid: ProviderEnum.BeninElectric,
            ElectricityBiller.EnuguPrepaid: ProviderEnum.EnuguElectric,
            ElectricityBiller.EnuguPostpaid: ProviderEnum.EnuguElectric,
            ElectricityBiller.EkoPrepaid: ProviderEnum.EkoElectric,
            ElectricityBiller.EkoPostpaid: ProviderEnum.EkoElectric,
            ElectricityBiller.IbadanPrepaid: ProviderEnum.IbadanElectric,
            ElectricityBiller.IbadanPostPaid: ProviderEnum.IbadanElectric,
            ElectricityBiller.IkejaPrepaid: ProviderEnum.IkejaElectric,
            ElectricityBiller.IkejaPostpaid: ProviderEnum.IkejaElectric,
            ElectricityBiller.JosPrepaid: ProviderEnum.JosElectric,
            ElectricityBiller.JosPostpaid: ProviderEnum.JosElectric,
            ElectricityBiller.PortharcourtPrepaid: ProviderEnum.PortharcourtElectric,
            ElectricityBiller.PortharcourtPostpaid: ProviderEnum.PortharcourtElectric,
            ElectricityBiller.KadunaPrepaid: ProviderEnum.KadunaElectric,
            ElectricityBiller.KadunaPostpaid: ProviderEnum.KadunaElectric,
            ElectricityBiller.KanoPrepaid: ProviderEnum.KanoElectric,
            ElectricityBiller.KanoPostpaid: ProviderEnum.KanoElectric,
            ElectricityBiller.YolaPrepaid: ProviderEnum.YolaElectric,
            ElectricityBiller.YolaPostpaid: ProviderEnum.YolaElectric,
        }

        return mapping[self]

    @property
    def disco(self) -> str:
        # e.g., "abuja_electric_postpaid" → "ABUJA"
        return self.split("_")[0].upper()

    @property
    def disco_type(self) -> str:
        # e.g., "abuja_electric_postpaid" → "POSTPAID"
        return self.split("_")[-1].upper()


class VasGateStatus(CustomEnum):
    Success = "success"
    Failed = "failed"
    Pending = "pending"
